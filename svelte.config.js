import adapter from '@sveltejs/adapter-auto';
import { vitePreprocess } from '@sveltejs/vite-plugin-svelte';

/** @type {import('@sveltejs/kit').Config} */
const config = {
	// Consult https://svelte.dev/docs/kit/integrations
	// for more information about preprocessors
	preprocess: vitePreprocess(),

	kit: {
		// adapter-auto only supports some environments, see https://svelte.dev/docs/kit/adapter-auto for a list.
		// If your environment is not supported, or you settled on a specific environment, switch out the adapter.
		// See https://svelte.dev/docs/kit/adapters for more information about adapters.
		adapter: adapter(),		
    paths: {
      // 关键：使用环境变量设置基础路径
      base: process.env.PUBLIC_BASE_URL ? new URL(process.env.PUBLIC_BASE_URL).pathname : '',
      relative: false
    },
    // 确保客户端也能访问环境变量
    env: {
      publicPrefix: 'VITE_PUBLIC_'
    }
	}
};

export default config;
