<script lang="ts">
  import { page } from '$app/stores'
  import { enhance } from '$app/forms'
  import type { ActionData } from './$types'

  export let form: ActionData

  let loading = false
  let error = ''
  let showPassword = false

  // 调试：打印form数据
  $: console.log('Form data:', form)

  // 检查表单错误
  $: if (form?.error) {
    error = form.error
  } else if ($page.url.searchParams.has('error')) {
    // 检查URL参数中的错误信息
    const errorType = $page.url.searchParams.get('error')
    if (errorType === 'CredentialsSignin') {
      error = '邮箱或密码错误'
    } else {
      error = '登录失败，请重试'
    }
  } else {
    error = ''
  }

  // 由于现在服务器端直接重定向，不需要客户端处理重定向
  // 如果到达这里说明登录失败或者还没有提交表单
</script>

<svelte:head>
  <title>登录 - Excel学习平台</title>
  <meta name="description" content="登录Excel学习平台，继续您的学习之旅" />
</svelte:head>

<div class="min-h-screen flex items-center justify-center bg-gradient-to-br from-green-50 via-white to-emerald-50 py-4 px-4 sm:px-6 lg:px-8">
  <div class="max-w-md w-full">
    <!-- 登录卡片 -->
    <div class="bg-white shadow-2xl rounded-3xl p-6 sm:p-8 border border-gray-100">
      <!-- 头部 -->
      <div class="text-center mb-6">
        <!-- Logo -->
        <div class="mx-auto w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-500 rounded-2xl flex items-center justify-center mb-3 shadow-lg">
          <span class="text-white text-2xl">📊</span>
        </div>

        <h2 class="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent mb-2">
          欢迎回来
        </h2>
        <p class="text-gray-600">
          登录到Excel学习平台，继续你的学习之旅
        </p>
        <p class="mt-3 text-sm text-gray-500">
          还没有账户？{' '}
          <a href="/auth/signup" class="font-semibold text-green-600 hover:text-green-700 transition-colors">
            立即注册 →
          </a>
        </p>
      </div>

      <!-- 登录表单 -->
      <form
        class="space-y-5"
        method="POST"
        use:enhance={({ formData }) => {
          loading = true
          error = ''
          console.log('Form submitting with data:', Object.fromEntries(formData))

          return async ({ result, update }) => {
            loading = false
            console.log('Form result:', result)

            if (result.type === 'failure') {
              error = (result.data as any)?.error || '登录失败，请重试'
            } else {
              // 对于成功和重定向，调用 update() 让 SvelteKit 处理
              await update()
            }
          }
        }}
      >
        <!-- 隐藏字段 -->
        <!-- <input type="hidden" name="callbackUrl" value="/dashboard" /> -->

        <div class="space-y-4">
          <!-- 邮箱地址 -->
          <div>
            <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
              邮箱地址
            </label>
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207" />
                </svg>
              </div>
              <input
                id="email"
                name="email"
                type="email"
                autocomplete="email"
                required
                value={form?.email ?? ''}
                class="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-xl placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200"
                placeholder="请输入邮箱地址"
              />
            </div>
          </div>

          <!-- 密码 -->
          <div>
            <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
              密码
            </label>
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
              </div>
              <input
                id="password"
                name="password"
                type={showPassword ? "text" : "password"}
                autocomplete="current-password"
                required
                class="block w-full pl-10 pr-12 py-3 border border-gray-300 rounded-xl placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200"
                placeholder="请输入密码"
              />
              <button
                type="button"
                class="absolute inset-y-0 right-0 pr-3 flex items-center hover:bg-gray-50 rounded-r-xl transition-colors"
                on:click={() => showPassword = !showPassword}
                aria-label={showPassword ? "隐藏密码" : "显示密码"}
              >
                {#if showPassword}
                  <!-- 隐藏密码图标 -->
                  <svg class="h-5 w-5 text-gray-400 hover:text-gray-600 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z" />
                    <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                {:else}
                  <!-- 显示密码图标 -->
                  <svg class="h-5 w-5 text-gray-400 hover:text-gray-600 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M3.98 8.223A10.477 10.477 0 001.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.45 10.45 0 0112 4.5c4.756 0 8.773 3.162 10.065 7.498a10.523 10.523 0 01-4.293 5.774M6.228 6.228L3 3m3.228 3.228l3.65 3.65m7.894 7.894L21 21m-3.228-3.228l-3.65-3.65m0 0a3 3 0 11-4.243-4.243m4.242 4.242L9.88 9.88" />
                  </svg>
                {/if}
              </button>
            </div>
          </div>
        </div>

        <!-- 忘记密码链接 -->
        <div class="flex justify-end -mt-1">
          <a href="/auth/forgot-password" class="text-sm text-green-600 hover:text-green-700 font-medium transition-colors">
            忘记密码？
          </a>
        </div>

        <!-- 错误消息 -->
        {#if error}
          <div class="bg-red-50 border border-red-200 rounded-xl p-4">
            <div class="flex items-center space-x-3">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div class="text-red-700 text-sm">{error}</div>
            </div>
          </div>
        {/if}

        <!-- 登录按钮 -->
        <button
          type="submit"
          disabled={loading}
          class="w-full flex justify-center items-center py-3 px-4 border border-transparent text-base font-semibold rounded-xl text-white bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200"
        >
          {#if loading}
            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            登录中...
          {:else}
            登录 →
          {/if}
        </button>
      </form>
    </div>
  </div>
</div>